<?php

namespace Tests\Feature;

use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;

class Laravel12UpgradeTest extends TestCase
{
    /**
     * Test that Laravel 12 is running correctly.
     */
    public function test_laravel_12_is_running(): void
    {
        $this->assertEquals('12.20.0', app()->version());
    }

    /**
     * Test that health check endpoint works.
     */
    public function test_health_check_endpoint(): void
    {
        $response = $this->get('/up');
        $response->assertStatus(200);
    }

    /**
     * Test that API endpoint works.
     */
    public function test_api_endpoint(): void
    {
        $response = $this->get('/api');
        $response->assertStatus(200);
        $response->assertJson(['message' => 'Welcome to GIPA']);
    }

    /**
     * Test that middleware is working.
     */
    public function test_middleware_is_working(): void
    {
        // Test that ForceJsonResponse middleware is working
        $response = $this->get('/api/nonexistent');
        $response->assertHeader('Content-Type', 'application/json');
    }

    /**
     * Test that routes are properly cached.
     */
    public function test_routes_are_cached(): void
    {
        $this->artisan('route:cache')->assertExitCode(0);
        $this->artisan('route:clear')->assertExitCode(0);
    }

    /**
     * Test that config is properly cached.
     */
    public function test_config_is_cached(): void
    {
        $this->artisan('config:cache')->assertExitCode(0);
        $this->artisan('config:clear')->assertExitCode(0);
    }
}
