<?php

namespace App\Providers;

use App\Models\Team;
use App\Policies\TeamPolicy;
use Carbon\Carbon;
use Illuminate\Foundation\Support\Providers\AuthServiceProvider as ServiceProvider;
use Laravel\Passport\Passport;

class AuthServiceProvider extends ServiceProvider
{
    /**
     * The policy mappings for the application.
     *
     * @var array
     */
    protected $policies = [
    ];

    /**
     * Register any authentication / authorization services.
     *
     * @return void
     */
    public function boot()
    {
        $this->registerPolicies();

        // Passport::routes() is no longer needed in Laravel 12
        // Routes are automatically registered

        Passport::personalAccessTokensExpireIn(now()->addDays(7));

        Passport::tokensCan([
            'student' => 'Student Type',
            'lecturer' => 'Lecturer Type',
            'administration' => 'Administration Type'
        ]);
    }
}
