{"name": "laravel/laravel", "type": "project", "description": "The Laravel Framework.", "keywords": ["framework", "laravel"], "license": "MIT", "require": {"php": "^8.2", "barryvdh/laravel-dompdf": "^3.0", "darkaonline/l5-swagger": "^9.0", "guzzlehttp/guzzle": "^7.2", "laravel/framework": "^12.0", "laravel/passport": "^13.0", "laravel/sanctum": "^4.0", "laravel/socialite": "^5.5", "laravel/tinker": "^2.7", "maatwebsite/excel": "^3.1", "milon/barcode": "^12.0", "mpdf/mpdf": "^8.2", "predis/predis": "^2.2", "psr/simple-cache": "^1.0|^2.0|^3.0", "pusher/pusher-php-server": "^7.2", "tightenco/ziggy": "^2.0"}, "require-dev": {"barryvdh/laravel-ide-helper": "^3.0", "fakerphp/faker": "^1.9.1", "getsolaris/laravel-make-service": "^1.1", "laravel/sail": "^1.0.1", "mockery/mockery": "^1.4.4", "nunomaduro/collision": "^8.0", "phpunit/phpunit": "^11.0", "spatie/laravel-ignition": "^2.0"}, "autoload": {"psr-4": {"App\\": "app/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/"}}, "autoload-dev": {"psr-4": {"Tests\\": "tests/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/"}}, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi"], "post-update-cmd": ["@php artisan vendor:publish --tag=laravel-assets --ansi --force"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi"]}, "extra": {"laravel": {"dont-discover": []}}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true, "allow-plugins": {"php-http/discovery": true}}, "minimum-stability": "dev", "prefer-stable": true}